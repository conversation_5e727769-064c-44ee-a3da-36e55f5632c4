"""
DeepSearch核心模块
实现完整的深度搜索流程
"""
from typing import List, Dict
import json
import time
import asyncio

from core.config import get_config
from modules.llm.llm_client import LLMClient, default_llm_client
from modules.common.schema import SearchResult, CodeSnippet
from modules.common.constant import SearchToolEnum
from modules.deepsearch.prompts import (
    SUBQUERY_FILTER_PROMPT, 
    GENERATE_NEW_QUERY_PROMPT,
    SYSTEM_PROMPTS
)
from utils.logger import logger

class DeepSearch:
    """深度搜索主类"""
    
    def __init__(
        self, 
        repo_path: str, 
        repo_info: str = "",
        llm_client: LLMClient = None,
        search_tools: List[SearchToolEnum] = [SearchToolEnum.GREP]
    ):
        """
        初始化DeepSearch
        
        Args:
            repo_path: 仓库路径
            repo_info: 仓库信息描述
            llm_client: LLM客户端，如果为None则使用默认客户端
            search_type: 搜索类型
        """
        self.repo_path = repo_path
        self.repo_info = repo_info
        self.search_tools = search_tools

        self.llm_client = llm_client or default_llm_client
        self.search_manager = search_tools[0].search_class(repo_path) # TODO: 支持多种搜索工具
        self.config = get_config().deepsearch
    
    def search(self, query: str) -> SearchResult:
        """
        执行深度搜索
        
        Args:
            query: 用户查询
            
        Returns:
            SearchResult: 搜索结果
        """
        result = SearchResult(original_query=query)
        
        logger.info(f"Start DeepSearch for Query: {query}")
        for iteration in range(self.config.max_iterations):
            result.iterations = iteration + 1

            # 判断是否需要生成新查询
            new_queries = self._generate_new_queries(
                query, 
                result.all_queries, 
                result.code_snippets
            )
            
            if not new_queries:
                logger.info(f"No New Queries Generated, Search Completed")
                break
            
            logger.info(f"Iteration {iteration + 1}: {query}")
            logger.info(f"Iteration {iteration + 1}: Generated {len(new_queries)} New Queries: {new_queries}")

            result.all_queries.extend(new_queries)
            
            # 搜索新查询
            new_snippets = self._search_and_filter(new_queries, query)
            result.code_snippets.extend(new_snippets)
            
            if not new_snippets:
                logger.info("新查询未找到相关代码，搜索结束")
                break
        
        # 4. 文件级别合并和去重
        logger.info("Merging Code Snippets at File Level...")
        result.code_snippets = self._deduplicate_snippets(result.code_snippets)
        
        logger.info(f"DeepSearch Completed: {result.get_summary()}")
        
        return result
    
    async def search_strem(self, query: str):
        """流式搜索"""
        """
        执行深度搜索
        
        Args:
            query: 用户查询
            
        Returns:
            SearchResult: 搜索结果
        """
        try:
            result = SearchResult(original_query=query)
        
            logger.info(f"Start DeepSearch for Query: {query}")
            yield f'data: {json.dumps({"type": "start", "message": "开始搜索", "timestamp": time.time()})}\n\n'
            await asyncio.sleep(0.01)  # 确保数据被及时发送

            for iteration in range(self.config.max_iterations):
                result.iterations = iteration + 1
                yield f'data: {json.dumps({"type": "process", "message": f"开始第{iteration + 1}次迭代", "timestamp": time.time()})}\n\n'
                await asyncio.sleep(0.01)

                new_queries = []
                
                try:
                    new_queries = self._generate_new_queries(
                        query,
                        result.all_queries,
                        result.code_snippets
                    )
                except Exception as e:
                    logger.error(f"生成新查询失败: {e}")
                    yield f'data: {json.dumps({"type": "error", "message": f"第{iteration + 1}次搜索: 生成新查询失败: {e}", "timestamp": time.time()})}\n\n'
                    break
                
                logger.info(f"Iteration {iteration + 1}: {query}")
                logger.info(f"Iteration {iteration + 1}: Generated {len(new_queries)} New Queries: {new_queries}")
                if len(new_queries) == 0:
                    yield f'data: {json.dumps({"type": "complete", "message": f"第{iteration + 1}次搜索: 未生成新查询，搜索结束", "timestamp": time.time()})}\n\n'
                    break

                new_queries_str = '\n'.join([f"{i+1}. {q}" for i, q in enumerate(new_queries)])
                yield f'data: {json.dumps({"type": "process", "message": f"第{iteration + 1}次搜索查询列表:\n" + new_queries_str, "timestamp": time.time()})}\n\n'
                await asyncio.sleep(0.01)

                result.all_queries.extend(new_queries)

                # 搜索新查询
                yield f'data: {json.dumps({"type": "process", "message": f"第{iteration + 1}次搜索: 开始执行搜索...", "timestamp": time.time()})}\n\n'
                try:
                    new_snippets = self._search_and_filter(new_queries, query)
                    yield f'data: {json.dumps({"type": "process", "message": f"第{iteration + 1}次搜索: 搜索完成，找到 {len(new_snippets)} 个代码片段", "timestamp": time.time()})}\n\n'
                except Exception as e:
                    logger.error(f"搜索失败: {e}")
                    yield f'data: {json.dumps({"type": "error", "message": f"第{iteration + 1}次搜索: 搜索失败: {e}", "timestamp": time.time()})}\n\n'
                    break
                
                result.code_snippets.extend(new_snippets)
                await asyncio.sleep(0.01)
            
            # 4. 文件级别合并和去重
            logger.info("Merging Code Snippets at File Level...")
            yield f'data: {json.dumps({"type": "process", "message": "合并代码片段...", "timestamp": time.time()})}\n\n'
            result.code_snippets = self._deduplicate_snippets(result.code_snippets)
            
            logger.info(f"DeepSearch Completed: {result.get_summary()}")
            yield f'data: {json.dumps({"type": "process", "message": f"搜索完成: {result.get_summary()}", "timestamp": time.time()})}\n\n'
            
            logger.info("Search Code Snippets:")
            yield f'data: {json.dumps({"type": "process", "message": "搜索代码片段:", "timestamp": time.time()})}\n\n'
            
            for snippet in result.code_snippets:
                logger.info(f"  - {snippet.file_path}:{snippet.start_line}-{snippet.end_line}")
                yield f'data: {json.dumps({"type": "process", "message": f"  - {snippet.file_path}:{snippet.start_line}-{snippet.end_line}", "timestamp": time.time()})}\n\n'
                logger.info(f"    {snippet.get_full_content()}")
                yield f'data: {json.dumps({"type": "process", "message": f"    {snippet.get_full_content()}", "timestamp": time.time()})}\n\n'
                logger.info(f"    {'-' * 50}")
                yield f'data: {json.dumps({"type": "process", "message": f"    {'-' * 50}", "timestamp": time.time()})}\n\n'

            # 发送完成事件
            yield f'data: {json.dumps({"type": "complete", "message": "搜索完成", "timestamp": time.time()})}\n\n'
            await asyncio.sleep(0.01)  # 确保完成事件被及时发送

        except Exception as e:
            logger.error(f"DeepSearch Failed: {e}")
            yield f'data: {json.dumps({"type": "error", "message": f"搜索失败: {e}", "timestamp": time.time()})}\n\n'
    
    def _search_and_filter(self, queries: List[str], original_query: str) -> List[CodeSnippet]:
        """
        搜索并过滤代码片段
        
        Args:
            queries: 查询列表
            original_query: 原始查询（用于过滤）
            
        Returns:
            List[CodeSnippet]: 过滤后的代码片段
        """
        # 并行检索所有子查询
        
        search_results = {}
        
        # 使用线程池进行并发搜索（避免多进程的序列化问题）
        import concurrent.futures
        max_workers = min(len(queries), 4)  # 限制线程数
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有搜索任务
            future_to_query = {
                executor.submit(self.search_manager.search, query): query
                for query in queries
            }
            
            # 收集搜索结果
            for future in concurrent.futures.as_completed(future_to_query):
                query = future_to_query[future]
                try:
                    snippets = future.result()
                    logger.info(f"Query '{query}' Found {len(snippets)} Code Snippets")
                    search_results[query] = snippets
                except Exception as exc:
                    logger.info(f"Query '{query}' Search Failed: {exc}")
                    search_results[query] = []

        # 统计总的代码片段数量
        snippets = snippets[:20] if len(snippets) > 20 else snippets # TODO: 使用配置控制
        total_snippets = sum(len(snippets) for snippets in search_results.values())
        logger.info(f"Found {total_snippets} Code Snippets, Start Filtering...")

        # 并行过滤每个子查询的代码片段
        all_filtered_snippets = []
        
        # 简化的并行过滤：对每个子查询的结果进行过滤
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_query = {}
            
            for sub_query, snippets in search_results.items():
                if snippets:  # 只处理有结果的查询
                    future = executor.submit(
                        self._filter_snippets_for_query, 
                        snippets, 
                        sub_query, 
                        original_query
                    )
                    future_to_query[future] = sub_query
            
            # 收集过滤结果
            for future in concurrent.futures.as_completed(future_to_query):
                sub_query = future_to_query[future]
                try:
                    filtered = future.result()
                    all_filtered_snippets.extend(filtered)
                    logger.info(f" Sub Query '{sub_query}' Filtered {len(filtered)} Snippets")
                except Exception as exc:
                    logger.info(f"Sub Query '{sub_query}' Filter Failed: {exc}")

        # 去重
        unique_snippets = self._deduplicate_snippets(all_filtered_snippets)
        logger.info(f"Filtered Snippets Deduplicated to {len(unique_snippets)} Unique Snippets")
        
        return unique_snippets

    def _filter_snippets_for_query(self, snippets: List[CodeSnippet], sub_query: str, original_query: str) -> List[CodeSnippet]:
        """
        为特定子查询过滤代码片段
        
        Args:
            snippets: 代码片段列表
            sub_query: 子查询
            original_query: 原始查询
            
        Returns:
            List[CodeSnippet]: 过滤后的代码片段
        """
        filtered_snippets = []
        
        # 使用子查询和原始查询的组合来判断相关性
        combined_query = f"Original Query: {original_query}\\nSub Query: {sub_query}"
        
        for snippet in snippets:
            if self._is_relevant_snippet(snippet, combined_query):
                filtered_snippets.append(snippet)
        
        return filtered_snippets
    
    def _deduplicate_snippets(self, snippets: List[CodeSnippet]) -> List[CodeSnippet]:
        """
        去重代码片段（基于文件路径和行号）
        
        Args:
            snippets: 代码片段列表
            
        Returns:
            List[CodeSnippet]: 去重后的代码片段列表
        """
        seen_stack = []
        unique_snippets = []
        
        sorted_snippets = sorted(snippets, key=lambda x: (x.file_path, x.start_line, x.end_line))

        for snippet in snippets:
            # 使用文件路径和行号作为唯一标识
            key = (snippet.file_path, snippet.start_line, snippet.end_line)
            
            if seen_stack and seen_stack[-1][0] == key[0] and seen_stack[-1][1] <= key[1] <= seen_stack[-1][2]:
                # 更新seen_stack的end_line
                seen_stack[-1][2] = max(seen_stack[-1][2], key[2])
                
                # 如果end_line在前一个snippet后面，则合并snippet内容
                if unique_snippets[-1].end_line >= key[2]:
                    unique_snippets[-1].content += snippet[(unique_snippets[-1].end_line - snippet.start_line + 1):]
                
                # 更新end_line
                unique_snippets[-1].end_line = seen_stack[-1][2]
                    
            else:   
                seen_stack.append(list(key)) # 因为元素可能被修改，tuple是不可变元组，因此使用list存入
                unique_snippets.append(snippet)

        return unique_snippets
    
    def _is_relevant_snippet(self, snippet: CodeSnippet, query: str) -> bool:
        """
        判断代码片段是否与查询相关
        
        Args:
            snippet: 代码片段
            query: 查询字符串
            
        Returns:
            bool: 是否相关
        """
        prompt = SUBQUERY_FILTER_PROMPT.format(
            repository_info=self.repo_info,
            query=query,
            code_snippet=snippet.get_full_content()
        )
        
        response = self.llm_client.call(prompt, SYSTEM_PROMPTS["filter"])
        return response.strip().upper() == "YES"
    
    def _generate_new_queries(
        self, 
        original_query: str, 
        previous_queries: List[str], 
        code_snippets: List[CodeSnippet]
    ) -> List[str]:
        """
        生成新的查询
        
        Args:
            original_query: 原始查询
            previous_queries: 之前的查询列表
            code_snippets: 已找到的代码片段
            
        Returns:
            List[str]: 新查询列表
        """
        # 构建代码片段摘要
        code_summary = self._build_code_summary(code_snippets)
        
        prompt = GENERATE_NEW_QUERY_PROMPT.format(
            question=original_query,
            mini_questions=previous_queries,
            code_snippet=code_summary,
            tool_description=self.search_tools[0].description # TODO: 支持多种搜索工具
        )
        logger.info(f"Generating New prompt for: {prompt}")

        new_queries = self.llm_client.call_json(
            prompt,
            SYSTEM_PROMPTS["generate_new_query"]
        )
        
        # 限制新查询数量
        return new_queries[:self.config.max_new_queries]
    
    def _build_code_summary(self, snippets: List[CodeSnippet]) -> str:
        """
        构建代码片段摘要
        
        Args:
            snippets: 代码片段列表
            
        Returns:
            str: 代码摘要
        """
        if not snippets:
            return "暂无相关代码片段"
        
        # 按文件分组
        file_groups = {}
        for snippet in snippets:
            if snippet.file_path not in file_groups:
                file_groups[snippet.file_path] = []
            file_groups[snippet.file_path].append(snippet)
        
        # 构建摘要
        summary_parts = []
        for file_path, file_snippets in file_groups.items():
            summary_parts.append(f"\n文件: {file_path}")
            for snippet in file_snippets[:3]:  # 每个文件最多显示3个片段
                summary_parts.append(f"  行 {snippet.start_line}-{snippet.end_line}: {snippet.content[:100]}...")
        
        return "\n".join(summary_parts)